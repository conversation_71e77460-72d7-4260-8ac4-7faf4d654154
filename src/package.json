{"name": "<PERSON><PERSON><PERSON>", "displayName": "%extension.displayName%", "description": "%extension.description%", "publisher": "ecloud", "version": "3.24.0", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "ecloud"}, "repository": {"type": "url", "url": "https://ecloud.10086.cn/portal"}, "bugs": {"url": "https://docs.qq.com/sheet/DRmpzbHpSdkhLanVn?no_promotion=1&tab=BB08J2"}, "homepage": "https://ecloud.10086.cn/op-help-center/doc/article/78884", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["ai", "chat", "mcp", "dev", "agent", "ai coding", "AI编程", "ecloud", "移动云", "autocoding", "autocomplete", "chatgpt", "code generation", "codegen", "inline completion", "typescript", "javascript", "python", "go", "c#", "湛卢"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"submenus": [{"id": "zhanlu.contextMenu", "label": "%views.contextMenu.label%"}, {"id": "zhanlu.terminalMenu", "label": "%views.terminalMenu.label%"}], "viewsContainers": {"activitybar": [{"id": "zhanlu-ActivityBar", "title": "%views.activitybar.title%", "icon": "assets/icons/icon.svg"}]}, "views": {"zhanlu-ActivityBar": [{"type": "webview", "id": "zhanlu.SidebarProvider", "name": "%views.sidebar.name%"}]}, "commands": [{"command": "zhanlu.plusButtonClicked", "title": "%command.newTask.title%", "icon": "$(add)"}, {"command": "zhanlu.mcpButtonClicked", "title": "%command.mcpServers.title%", "icon": "$(server)"}, {"command": "zhanlu.promptsButtonClicked", "title": "%command.prompts.title%", "icon": "$(notebook)"}, {"command": "zhanlu.historyButtonClicked", "title": "%command.history.title%", "icon": "$(history)"}, {"command": "zhanlu.marketplaceButtonClicked", "title": "%command.marketplace.title%", "icon": "$(extensions)"}, {"command": "zhanlu.roomoteAgentButtonClicked", "title": "%command.roomoteAgent.title%", "icon": "$(cloud)"}, {"command": "zhanlu.popoutButtonClicked", "title": "%command.openInEditor.title%", "icon": "$(link-external)"}, {"command": "zhanlu.settingsButtonClicked", "title": "%command.settings.title%", "icon": "$(settings-gear)"}, {"command": "zhanlu.helpButtonClicked", "title": "%command.documentation.title%", "icon": "$(question)"}, {"command": "zhanlu.logoutButtonClicked", "title": "%command.logout.title%", "icon": "$(sign-out)"}, {"command": "zhanlu.openInNewTab", "title": "%command.openInNewTab.title%", "category": "%configuration.title%"}, {"command": "zhanlu.explainCode", "title": "%command.explainCode.title%", "category": "%configuration.title%"}, {"command": "zhanlu.fixCode", "title": "%command.fixCode.title%", "category": "%configuration.title%"}, {"command": "zhanlu.improveCode", "title": "%command.improveCode.title%", "category": "%configuration.title%"}, {"command": "zhanlu.addToContext", "title": "%command.addToContext.title%", "category": "%configuration.title%"}, {"command": "zhanlu.newTask", "title": "%command.newTask.title%", "category": "%configuration.title%"}, {"command": "zhanlu.terminalAddToContext", "title": "%command.terminal.addToContext.title%", "category": "Terminal"}, {"command": "zhanlu.terminalFixCommand", "title": "%command.terminal.fixCommand.title%", "category": "Terminal"}, {"command": "zhanlu.terminalExplainCommand", "title": "%command.terminal.explainCommand.title%", "category": "Terminal"}, {"command": "zhanlu.terminalFixCommandInCurrentTask", "title": "%command.terminal.fixCommandInCurrentTask.title%", "category": "Terminal"}, {"command": "zhanlu.terminalExplainCommandInCurrentTask", "title": "%command.terminal.explainCommandInCurrentTask.title%", "category": "Terminal"}, {"command": "zhanlu.setCustomStoragePath", "title": "%command.setCustomStoragePath.title%", "category": "%configuration.title%"}, {"command": "zhanlu.importSettings", "title": "%command.importSettings.title%", "category": "%configuration.title%"}, {"command": "zhanlu.focusInput", "title": "%command.focusInput.title%", "category": "%configuration.title%"}, {"command": "zhanlu.acceptInput", "title": "%command.acceptInput.title%", "category": "%configuration.title%"}, {"command": "zhanlu.unitTest", "title": "%command.unitTest.title%", "category": "%configuration.title%"}, {"command": "zhanlu.codeReview", "title": "%command.codeReview.title%", "category": "%configuration.title%"}, {"command": "zhanlu.commentCode", "title": "%command.commentCode.title%", "category": "%configuration.title%"}], "menus": {"editor/context": [{"submenu": "zhanlu.contextMenu", "group": "1"}], "zhanlu.contextMenu": [{"command": "zhanlu.addToContext", "group": "1_actions@1"}, {"command": "zhanlu.explainCode", "group": "1_actions@2"}, {"command": "zhanlu.fixCode", "group": "1_actions@3"}, {"command": "zhanlu.improveCode", "group": "1_actions@4"}, {"command": "zhanlu.unitTest", "group": "1_actions@5"}, {"command": "zhanlu.codeReview", "group": "1_actions@6"}, {"command": "zhanlu.commentCode", "group": "1_actions@7"}], "terminal/context": [{"submenu": "zhanlu.terminalMenu", "group": "2"}], "zhanlu.terminalMenu": [{"command": "zhanlu.terminalAddToContext", "group": "1_actions@1"}, {"command": "zhanlu.terminalFixCommand", "group": "1_actions@2"}, {"command": "zhanlu.terminalExplainCommand", "group": "1_actions@3"}, {"command": "zhanlu.terminalFixCommandInCurrentTask", "group": "1_actions@5"}, {"command": "zhanlu.terminalExplainCommandInCurrentTask", "group": "1_actions@6"}], "view/title": [{"command": "zhanlu.plusButtonClicked", "group": "navigation@1", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.promptsButtonClicked", "group": "navigation@2", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.mcpButtonClicked", "group": "navigation@3", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.marketplaceButtonClicked", "group": "navigation@4", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.roomoteAgentButtonClicked", "group": "navigation@5", "when": "view == zhanlu.SidebarProvider && zhanlu.roomoteAgentEnabled"}, {"command": "zhanlu.historyButtonClicked", "group": "navigation@6", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.popoutButtonClicked", "group": "navigation@7", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.settingsButtonClicked", "group": "navigation@9", "when": "view == zhanlu.SidebarProvider"}, {"command": "zhanlu.logoutButtonClicked", "group": "navigation@10", "when": "view == zhanlu.SidebarProvider"}], "editor/title": [{"command": "zhanlu.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.promptsButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.mcpButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.marketplaceButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.roomoteAgentButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider && zhanlu.roomoteAgentEnabled"}, {"command": "zhanlu.historyButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.settingsButtonClicked", "group": "navigation@8", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}, {"command": "zhanlu.logoutButtonClicked", "group": "navigation@9", "when": "activeWebviewPanelId == zhanlu.TabPanelProvider"}]}, "configuration": {"title": "%configuration.title%", "properties": {"zhanlu.developerMode": {"order": -1, "type": "boolean", "default": false, "description": "%settings.developerMode.description%"}, "zhanlu.allowedCommands": {"order": 0, "type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "%commands.allowedCommands.description%"}, "zhanlu.deniedCommands": {"type": "array", "items": {"type": "string"}, "default": [], "description": "%commands.deniedCommands.description%"}, "zhanlu.commandExecutionTimeout": {"type": "number", "default": 0, "minimum": 0, "maximum": 600, "description": "%commands.commandExecutionTimeout.description%"}, "zhanlu.commandTimeoutAllowlist": {"type": "array", "items": {"type": "string"}, "default": [], "description": "%commands.commandTimeoutAllowlist.description%"}, "zhanlu.preventCompletionWithOpenTodos": {"type": "boolean", "default": false, "description": "%commands.preventCompletionWithOpenTodos.description%"}, "zhanlu.customStoragePath": {"order": 2, "type": "string", "default": "", "description": "%settings.customStoragePath.description%"}, "zhanlu.completion.debounce_time": {"order": 3, "type": "number", "default": 500, "enum": [500, 1000, 1500], "description": "%settings.completion.debounce_time.description%"}, "zhanlu.completion.completion_number": {"order": 4, "type": "number", "default": 1, "enum": [1, 2, 3], "description": "%settings.completion.completion_number.description%"}, "zhanlu.completion.inlineCompletion_granularity": {"order": 5, "type": "string", "default": "均衡", "enum": ["单行", "一次性最大化", "均衡"], "enumDescriptions": ["%settings.completion.inlineCompletion_granularity.singleRow%", "%settings.completion.inlineCompletion_granularity.oneTimeMaximization%", "%settings.completion.inlineCompletion_granularity.balanced%"], "description": "%settings.completion.inlineCompletion_granularity.description%"}, "zhanlu.completion.multiple_line_Completion": {"order": 6, "type": "string", "default": "自动补全", "enum": ["自动补全", "触发补全"], "enumDescriptions": ["%settings.completion.multiple_line_Completion.autoCompletion.description%", "%settings.completion.multiple_line_Completion.triggerCompletion.description%"], "description": "%settings.completion.multiple_line_Completion.description%"}, "zhanlu.completion.max_tokens_completion": {"order": 7, "type": "number", "default": 64, "description": "%settings.completion.max_tokens_completion.description%", "exclusiveMinimum": 0}, "zhanlu.dmt.maxTokens": {"order": 9, "type": "number", "default": 4096, "description": "%settings.dmt.maxTokens.description%", "exclusiveMinimum": 0}, "zhanlu.serverBaseUrl": {"order": 10, "type": "string", "default": "https://api-wuxi-1.cmecloud.cn:8443", "description": "%settings.serverBaseUrl.description%"}}}}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "pnpm bundle --production", "vsix": "mkdirp ../bin && vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "cd .. && tsc --noEmit --watch --project src/tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.848.0", "@aws-sdk/credential-providers": "^3.848.0", "@gitbeaker/rest": "^42.5.0", "@google/genai": "^1.0.0", "@lmstudio/sdk": "^1.1.1", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@qdrant/js-client-rest": "^1.14.0", "@roo-code/cloud": "workspace:^", "@roo-code/ipc": "workspace:^", "@roo-code/telemetry": "workspace:^", "@roo-code/types": "workspace:^", "@types/lodash.debounce": "^4.0.9", "@types/node-rsa": "^1.1.4", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "eventsource-parser": "^3.0.3", "exceljs": "^4.4.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.0.0", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "i18next": "^25.0.0", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "jira.js": "^5.2.1", "lodash.debounce": "^4.0.8", "lru-cache": "^11.1.0", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "node-rsa": "^1.1.1", "openai": "^5.0.0", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^5.0.0", "pretty-bytes": "^7.0.0", "proper-lockfile": "^4.1.2", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^24.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^12.0.0", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "stream-json": "^1.8.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.12", "turndown": "^7.2.0", "uri-js": "^4.4.1", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.25.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/build": "workspace:^", "@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/proper-lockfile": "^4.1.4", "@types/ps-tree": "^1.1.6", "@types/stream-json": "^1.7.8", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.4", "rimraf": "^6.0.1", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.2.3", "zod-to-ts": "^1.2.0"}}